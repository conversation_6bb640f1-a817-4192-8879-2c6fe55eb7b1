/**
 * 物流状态分析器使用示例
 * 展示如何使用新版本的物流状态分析器
 */

// 基础使用示例
export async function basicUsageExample() {
  console.log('=== 基础使用示例 ===')
  
  // 导入分析函数
  const { analyzeLogisticsStatus } = await import('./index.js')
  
  // 模拟物流轨迹数据
  const orderTrack = [
    {
      context: '快递员正在派送中，请保持电话畅通',
      msgTime: '2024-01-15T10:30:00Z',
      content: '快递员正在派送中，请保持电话畅通'
    },
    {
      context: '快件已到达【北京朝阳区分拣中心】',
      msgTime: '2024-01-15T08:20:00Z',
      content: '快件已到达【北京朝阳区分拣中心】'
    },
    {
      context: '快件已从【上海浦东分拣中心】发出',
      msgTime: '2024-01-14T18:45:00Z',
      content: '快件已从【上海浦东分拣中心】发出'
    }
  ]
  
  // 分析物流状态
  const result = await analyzeLogisticsStatus(orderTrack, {
    enableAI: false,  // 暂时关闭AI，避免依赖问题
    enableCache: true,
    maxAnalysisDepth: 5
  })
  
  console.log('分析结果:', {
    状态: result.statusText,
    置信度: `${Math.round(result.confidence * 100)}%`,
    紧急程度: result.urgency,
    分析方法: result.analysisMethod,
    建议: result.recommendations
  })
  
  return result
}

// 详细分析示例
export async function detailedAnalysisExample() {
  console.log('\n=== 详细分析示例 ===')
  
  const { getDetailedLogisticsAnalysis } = await import('./index.js')
  
  const orderTrack = [
    {
      context: '已签收，签收人：张三，感谢使用顺丰速运',
      msgTime: '2024-01-15T14:20:00Z'
    },
    {
      context: '派送员已出发，正在派送中',
      msgTime: '2024-01-15T13:45:00Z'
    }
  ]
  
  const detailedResult = await getDetailedLogisticsAnalysis(orderTrack, {
    enableAI: false
  })
  
  console.log('详细分析结果:', {
    状态: detailedResult.statusText,
    置信度: `${Math.round(detailedResult.confidence * 100)}%`,
    是否终态: detailedResult.isFinal,
    紧急程度: detailedResult.urgency,
    轨迹数量: detailedResult.trackCount,
    建议: detailedResult.recommendations,
    分析时间: detailedResult.analysisTimestamp
  })
  
  return detailedResult
}

// 快速检测示例
export async function quickDetectionExample() {
  console.log('\n=== 快速检测示例 ===')
  
  const { quickStatusDetection } = await import('./index.js')
  
  const testCases = [
    '已签收',
    '派送失败，收件人不在',
    '运输中',
    '等待揽件',
    '包裹异常'
  ]
  
  for (const context of testCases) {
    const result = await quickStatusDetection(context, {
      msgTime: new Date().toISOString()
    })
    
    console.log(`"${context}" -> ${result.statusText} (${Math.round(result.confidence * 100)}%)`)
  }
}

// 批量分析示例
export async function batchAnalysisExample() {
  console.log('\n=== 批量分析示例 ===')
  
  const { batchAnalyzeOrders } = await import('./index.js')
  
  const orders = [
    {
      orderId: 'ORDER001',
      orderTrack: [
        { context: '已签收', msgTime: '2024-01-15T14:00:00Z' }
      ]
    },
    {
      orderId: 'ORDER002', 
      orderTrack: [
        { context: '派送中', msgTime: '2024-01-15T13:30:00Z' }
      ]
    },
    {
      orderId: 'ORDER003',
      orderTrack: [
        { context: '运输异常', msgTime: '2024-01-15T12:00:00Z' }
      ]
    }
  ]
  
  const batchResults = await batchAnalyzeOrders(orders, {
    enableAI: false
  })
  
  console.log('批量分析结果:')
  batchResults.forEach(result => {
    console.log(`${result.orderId}: ${result.statusText} (置信度: ${Math.round(result.confidence * 100)}%)`)
  })
  
  return batchResults
}

// 状态变化检测示例
export async function statusChangeExample() {
  console.log('\n=== 状态变化检测示例 ===')
  
  const { detectStatusChange, analyzeLogisticsStatus } = await import('./index.js')
  
  // 模拟之前的分析结果
  const previousAnalysis = {
    status: 'in_transit',
    statusText: '运输中',
    confidence: 0.8
  }
  
  // 当前分析结果
  const currentTrack = [
    { context: '正在派送中', msgTime: new Date().toISOString() }
  ]
  
  const currentAnalysis = await analyzeLogisticsStatus(currentTrack, { enableAI: false })
  
  // 检测变化
  const changeResult = detectStatusChange(previousAnalysis, currentAnalysis)
  
  console.log('状态变化检测:', {
    是否有变化: changeResult.hasChanged,
    之前状态: changeResult.previousStatus,
    当前状态: changeResult.currentStatus,
    置信度变化: changeResult.confidenceChange > 0 ? '提升' : '下降',
    变化幅度: Math.abs(changeResult.confidenceChange)
  })
  
  return changeResult
}

// 系统健康检查示例
export async function systemHealthExample() {
  console.log('\n=== 系统健康检查示例 ===')
  
  const { getSystemHealth } = await import('./index.js')
  
  const health = await getSystemHealth()
  
  console.log('系统健康状态:', {
    状态: health.status,
    AI可用: health.ai.available,
    缓存启用: health.cache.enabled,
    检查时间: health.timestamp
  })
  
  if (health.ai.models) {
    console.log('AI模型状态:', health.ai.models)
  }
  
  return health
}

// 运行所有示例
export async function runAllExamples() {
  console.log('🚀 物流状态分析器使用示例\n')
  
  try {
    await basicUsageExample()
    await detailedAnalysisExample()
    await quickDetectionExample()
    await batchAnalysisExample()
    await statusChangeExample()
    await systemHealthExample()
    
    console.log('\n✅ 所有示例运行完成！')
  } catch (error) {
    console.error('❌ 示例运行失败:', error)
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples()
}
