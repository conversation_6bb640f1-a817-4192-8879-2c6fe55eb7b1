/**
 * 物流状态分析器 - 工具函数模块
 * 提供高性能的工具函数和缓存机制
 */

import { 
  STATUS_TEXT_MAP, 
  STATUS_URGENCY_MAP, 
  PERFORMANCE_CONFIG,
  CONFIDENCE_THRESHOLDS,
  WEIGHT_CONFIG
} from './constants.js'
import { AMBIGUOUS_KEYWORDS } from './keywords.js'

// 缓存管理
const analysisCache = new Map()
const cacheTimestamps = new Map()

/**
 * 高性能关键词匹配分数计算
 * @param {string} text - 待分析文本
 * @param {Object} keywords - 分级关键词对象 {high: [], medium: [], low: []}
 * @returns {number} 匹配分数
 */
export function calculateKeywordScore(text, keywords) {
  // 缓存检查
  const cacheKey = `keyword_${text}_${JSON.stringify(keywords)}`
  if (PERFORMANCE_CONFIG.CACHE_ENABLED) {
    const cached = getFromCache(cacheKey)
    if (cached !== null) return cached
  }

  let score = 0
  const lowerText = text.toLowerCase()
  const textLength = text.length

  // 动态权重配置 - 根据文本长度调整
  const lengthFactor = Math.min(textLength / 20, 2)
  const weights = {
    high: Math.round(WEIGHT_CONFIG.KEYWORD_MATCH.HIGH / lengthFactor),
    medium: Math.round(WEIGHT_CONFIG.KEYWORD_MATCH.MEDIUM / lengthFactor),
    low: Math.round(WEIGHT_CONFIG.KEYWORD_MATCH.LOW / lengthFactor)
  }

  // 预编译正则表达式以提高性能
  const compiledPatterns = new Map()

  for (const [level, keywordList] of Object.entries(keywords)) {
    const weight = weights[level] || 8

    for (const keyword of keywordList) {
      const lowerKeyword = keyword.toLowerCase()
      let matchScore = 0

      // 支持正则表达式匹配
      if (keyword.includes('.*')) {
        try {
          let regex = compiledPatterns.get(lowerKeyword)
          if (!regex) {
            regex = new RegExp(lowerKeyword, 'i')
            compiledPatterns.set(lowerKeyword, regex)
          }
          
          if (regex.test(lowerText)) {
            matchScore = weight * 1.5 // 正则匹配额外加分
          }
        } catch (e) {
          // 正则表达式错误时降级为普通匹配
          const fallbackKeyword = lowerKeyword.replace(/\.\*/g, '')
          if (lowerText.includes(fallbackKeyword)) {
            matchScore = weight
          }
        }
      } else {
        // 智能关键词匹配
        if (lowerText.includes(lowerKeyword)) {
          // 计算匹配质量
          const matchQuality = calculateMatchQuality(lowerText, lowerKeyword)
          matchScore = weight * matchQuality
        }
      }

      // 避免重复计分（同一个关键词在文本中多次出现）
      if (matchScore > 0) {
        const escapedKeyword = lowerKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        const occurrences = (lowerText.match(new RegExp(escapedKeyword, 'g')) || []).length
        const occurrenceBonus = Math.min(occurrences * 0.2, 1) // 最多20%的额外加分
        score += matchScore * (1 + occurrenceBonus)
      }
    }
  }

  const finalScore = Math.round(score)
  
  // 缓存结果
  if (PERFORMANCE_CONFIG.CACHE_ENABLED) {
    setToCache(cacheKey, finalScore)
  }

  return finalScore
}

/**
 * 计算关键词匹配质量
 * @param {string} text - 文本
 * @param {string} keyword - 关键词
 * @returns {number} 匹配质量系数 (0.5-2.0)
 */
export function calculateMatchQuality(text, keyword) {
  // 完全匹配
  if (text.trim() === keyword) {
    return 2.0
  }

  // 独立词匹配（前后有空格或标点）
  const wordBoundaryRegex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i')
  if (wordBoundaryRegex.test(text)) {
    return 1.5
  }

  // 开头匹配
  if (text.startsWith(keyword)) {
    return 1.3
  }

  // 结尾匹配
  if (text.endsWith(keyword)) {
    return 1.2
  }

  // 普通包含匹配
  return 1.0
}

/**
 * 检测是否为模糊上下文
 * @param {string} context - 轨迹描述
 * @returns {boolean} 是否为模糊上下文
 */
export function isAmbiguousContext(context) {
  const lowerContext = context.toLowerCase()

  // 如果文本很短且只包含模糊词汇，认为是模糊的
  if (context.length < 10) {
    return AMBIGUOUS_KEYWORDS.some(keyword => lowerContext.includes(keyword))
  }

  // 如果文本中模糊词汇占比过高，认为是模糊的
  const ambiguousCount = AMBIGUOUS_KEYWORDS.filter(keyword =>
    lowerContext.includes(keyword)
  ).length

  return ambiguousCount >= 2 && context.length < 20
}

/**
 * 分析时间因素
 * @param {Object} trackRecord - 轨迹记录
 * @returns {Object} 时间分析结果
 */
export function analyzeTimeFactors(trackRecord) {
  let timeBonus = 0
  const timestamp = trackRecord.msgTime || trackRecord.time

  if (!timestamp) {
    return { timeBonus: 0, hasValidTime: false }
  }

  try {
    const trackTime = new Date(timestamp)
    const now = new Date()
    const timeDiff = now - trackTime
    const hoursDiff = timeDiff / (1000 * 60 * 60)

    // 最新记录时间越近，置信度越高
    if (hoursDiff <= 2) {
      timeBonus += 5 // 2小时内的记录
    } else if (hoursDiff <= 24) {
      timeBonus += 3 // 24小时内的记录
    } else if (hoursDiff <= 72) {
      timeBonus += 1 // 3天内的记录
    }

    return {
      timeBonus,
      hasValidTime: true,
      hoursSinceUpdate: hoursDiff,
      isRecent: hoursDiff <= 24,
      isVeryRecent: hoursDiff <= 2
    }
  } catch (error) {
    return { timeBonus: 0, hasValidTime: false }
  }
}

/**
 * 获取状态的紧急程度
 * @param {string} status - 物流状态
 * @returns {number} 紧急程度 (1-5, 5最紧急)
 */
export function getStatusUrgency(status) {
  return STATUS_URGENCY_MAP[status] || 2
}

/**
 * 生成基于分析结果的建议
 * @param {Object} analysis - 分析结果
 * @returns {Array} 建议列表
 */
export function generateRecommendations(analysis) {
  const recommendations = []

  if (analysis.confidence < CONFIDENCE_THRESHOLDS.MEDIUM) {
    recommendations.push('建议联系快递公司确认具体状态')
  }

  if (analysis.status === 'failed_delivery') {
    recommendations.push('建议主动联系收件人确认地址和联系方式')
  }

  if (analysis.status === 'exception') {
    recommendations.push('建议立即联系快递公司处理异常情况')
  }

  if (analysis.status === 'out_for_delivery' && analysis.timeAnalysis?.hoursSinceUpdate > 24) {
    recommendations.push('派送时间较长，建议联系派送员确认情况')
  }

  if (analysis.status === 'in_transit' && analysis.timeAnalysis?.hoursSinceUpdate > 72) {
    recommendations.push('运输时间较长，建议查询是否有延误')
  }

  if (analysis.status === 'pending_pickup' && analysis.timeAnalysis?.hoursSinceUpdate > 48) {
    recommendations.push('揽件时间较长，建议联系寄件网点')
  }

  return recommendations
}

/**
 * 格式化分析结果
 * @param {Object} analysis - 原始分析结果
 * @returns {Object} 格式化后的结果
 */
export function formatAnalysisResult(analysis) {
  return {
    ...analysis,
    statusText: STATUS_TEXT_MAP[analysis.status] || '未知状态',
    urgency: getStatusUrgency(analysis.status),
    recommendations: generateRecommendations(analysis),
    formattedConfidence: `${Math.round(analysis.confidence * 100)}%`,
    analysisTimestamp: new Date().toISOString()
  }
}

/**
 * 批量分析轨迹记录
 * @param {Array} trackRecords - 轨迹记录数组
 * @param {Function} analyzeFunction - 分析函数
 * @returns {Array} 批量分析结果
 */
export function batchAnalyzeTrackRecords(trackRecords, analyzeFunction) {
  const results = []
  const batchSize = PERFORMANCE_CONFIG.BATCH_SIZE

  for (let i = 0; i < trackRecords.length; i += batchSize) {
    const batch = trackRecords.slice(i, i + batchSize)
    const batchResults = batch.map(record => {
      try {
        return analyzeFunction(record)
      } catch (error) {
        console.warn('轨迹分析失败:', error)
        return {
          error: error.message,
          record
        }
      }
    })
    results.push(...batchResults)
  }

  return results
}

/**
 * 缓存管理 - 获取缓存
 * @param {string} key - 缓存键
 * @returns {*} 缓存值或null
 */
function getFromCache(key) {
  if (!PERFORMANCE_CONFIG.CACHE_ENABLED) return null

  const timestamp = cacheTimestamps.get(key)
  if (!timestamp || Date.now() - timestamp > PERFORMANCE_CONFIG.CACHE_TTL) {
    // 缓存过期
    analysisCache.delete(key)
    cacheTimestamps.delete(key)
    return null
  }

  return analysisCache.get(key) || null
}

/**
 * 缓存管理 - 设置缓存
 * @param {string} key - 缓存键
 * @param {*} value - 缓存值
 */
function setToCache(key, value) {
  if (!PERFORMANCE_CONFIG.CACHE_ENABLED) return

  // 清理过期缓存
  if (analysisCache.size > 1000) {
    clearExpiredCache()
  }

  analysisCache.set(key, value)
  cacheTimestamps.set(key, Date.now())
}

/**
 * 清理过期缓存
 */
function clearExpiredCache() {
  const now = Date.now()
  const expiredKeys = []

  for (const [key, timestamp] of cacheTimestamps.entries()) {
    if (now - timestamp > PERFORMANCE_CONFIG.CACHE_TTL) {
      expiredKeys.push(key)
    }
  }

  expiredKeys.forEach(key => {
    analysisCache.delete(key)
    cacheTimestamps.delete(key)
  })
}

/**
 * 清空所有缓存
 */
export function clearAllCache() {
  analysisCache.clear()
  cacheTimestamps.clear()
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计
 */
export function getCacheStats() {
  return {
    size: analysisCache.size,
    enabled: PERFORMANCE_CONFIG.CACHE_ENABLED,
    ttl: PERFORMANCE_CONFIG.CACHE_TTL,
    oldestEntry: Math.min(...cacheTimestamps.values()),
    newestEntry: Math.max(...cacheTimestamps.values())
  }
}

/**
 * 文本预处理
 * @param {string} text - 原始文本
 * @returns {string} 预处理后的文本
 */
export function preprocessText(text) {
  if (!text || typeof text !== 'string') return ''

  return text
    .trim()
    .replace(/\s+/g, ' ') // 合并多个空格
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s\-_.,;:!?()[\]{}]/g, '') // 保留中文、英文、数字和常用标点
}

/**
 * 计算文本相似度
 * @param {string} text1 - 文本1
 * @param {string} text2 - 文本2
 * @returns {number} 相似度 (0-1)
 */
export function calculateTextSimilarity(text1, text2) {
  if (!text1 || !text2) return 0

  const set1 = new Set(text1.toLowerCase().split(''))
  const set2 = new Set(text2.toLowerCase().split(''))
  
  const intersection = new Set([...set1].filter(x => set2.has(x)))
  const union = new Set([...set1, ...set2])
  
  return intersection.size / union.size
}

/**
 * 性能监控装饰器
 * @param {Function} fn - 要监控的函数
 * @param {string} name - 函数名称
 * @returns {Function} 装饰后的函数
 */
export function withPerformanceMonitoring(fn, name) {
  return function(...args) {
    const startTime = performance.now()
    try {
      const result = fn.apply(this, args)
      const endTime = performance.now()
      
      // 只在开发环境记录性能
      if (process.env.NODE_ENV === 'development') {
        console.debug(`${name} 执行时间: ${(endTime - startTime).toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const endTime = performance.now()
      console.warn(`${name} 执行失败 (${(endTime - startTime).toFixed(2)}ms):`, error)
      throw error
    }
  }
}
