/**
 * 物流状态分析器 - 语义分析模块
 * 集成compromise库和自定义语义分析逻辑
 * 提供高性能的语义理解能力
 */

import { LOGISTICS_STATUS } from './constants.js'
import { 
  STRONG_POSITIVE_KEYWORDS, 
  STRONG_NEGATIVE_KEYWORDS,
  TIME_LOGIC_KEYWORDS,
  CAUSAL_KEYWORDS,
  LOCATION_KEYWORDS
} from './keywords.js'

// compromise库的懒加载
let nlpLib = null
let isNlpLoading = false

/**
 * 懒加载compromise库
 * @returns {Promise<boolean>} 加载是否成功
 */
async function loadCompromise() {
  if (nlpLib) return true
  if (isNlpLoading) {
    while (isNlpLoading) {
      await new Promise(resolve => setTimeout(resolve, 50))
    }
    return !!nlpLib
  }

  isNlpLoading = true
  try {
    const compromise = await import('compromise')
    nlpLib = compromise.default || compromise
    return true
  } catch (error) {
    console.warn('Compromise库加载失败:', error.message)
    return false
  } finally {
    isNlpLoading = false
  }
}

/**
 * 增强的语义分析
 * @param {string} context - 轨迹描述文本
 * @returns {Promise<Object>} 语义分析结果
 */
export async function enhancedSemanticAnalysis(context) {
  const results = await Promise.allSettled([
    basicSemanticAnalysis(context),
    compromiseSemanticAnalysis(context),
    contextCoherenceAnalysis(context),
    temporalSemanticAnalysis(context)
  ])

  // 合并所有成功的分析结果
  const successResults = results
    .filter(result => result.status === 'fulfilled')
    .map(result => result.value)

  return mergeSemanticResults(successResults, context)
}

/**
 * 基础语义分析
 * @param {string} context - 文本内容
 * @returns {Object} 基础分析结果
 */
function basicSemanticAnalysis(context) {
  const lowerContext = context.toLowerCase()
  let semanticScore = 0
  let intensity = 'normal'
  const features = {
    hasStrongPositive: false,
    hasStrongNegative: false,
    hasTimeLogic: false,
    hasCausal: false,
    hasLocation: false
  }

  // 检查强烈肯定词汇
  for (const keyword of STRONG_POSITIVE_KEYWORDS) {
    if (context.includes(keyword)) {
      semanticScore += 10
      features.hasStrongPositive = true
      intensity = 'high'
    }
  }

  // 检查强烈否定词汇
  for (const keyword of STRONG_NEGATIVE_KEYWORDS) {
    if (context.includes(keyword)) {
      semanticScore -= 10
      features.hasStrongNegative = true
      intensity = 'high'
    }
  }

  // 检查时间逻辑词汇
  for (const keyword of TIME_LOGIC_KEYWORDS) {
    if (context.includes(keyword)) {
      semanticScore += 2
      features.hasTimeLogic = true
    }
  }

  // 检查因果关系词汇
  for (const keyword of CAUSAL_KEYWORDS) {
    if (context.includes(keyword)) {
      semanticScore += 3
      features.hasCausal = true
    }
  }

  // 检查地点连续性词汇
  for (const keyword of LOCATION_KEYWORDS) {
    if (context.includes(keyword)) {
      semanticScore += 2
      features.hasLocation = true
    }
  }

  // 否定词检测
  const negationPatterns = [
    '未.*成功', '尚未.*', '暂未.*', '等待.*', '准备.*', '即将.*',
    '没有.*', '无法.*', '不能.*', '未能.*'
  ]

  for (const pattern of negationPatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore -= 5
    }
  }

  // 肯定词检测
  const affirmationPatterns = [
    '已.*完成', '成功.*', '顺利.*', '正常.*', '完成.*',
    '已经.*', '确实.*', '的确.*'
  ]

  for (const pattern of affirmationPatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore += 5
    }
  }

  return {
    semanticScore: Math.max(-15, Math.min(15, semanticScore)),
    intensity,
    features,
    method: 'basic'
  }
}

/**
 * 使用compromise库进行语义分析
 * @param {string} context - 文本内容
 * @returns {Promise<Object>} compromise分析结果
 */
async function compromiseSemanticAnalysis(context) {
  const compromiseLoaded = await loadCompromise()
  
  if (!compromiseLoaded) {
    return {
      semanticScore: 0,
      method: 'compromise_failed',
      error: 'Compromise库加载失败'
    }
  }

  try {
    const doc = nlpLib(context)

    // 提取语言学特征
    const verbs = doc.verbs().out('array')
    const nouns = doc.nouns().out('array')
    const adjectives = doc.adjectives().out('array')
    const places = doc.places().out('array')
    const people = doc.people().out('array')

    // 时态分析
    const tenseAnalysis = analyzeTensePattern(doc)
    
    // 情感分析
    const sentiment = analyzeSentimentWithCompromise(doc)

    // 计算语义加成分数
    let semanticBonus = 0

    // 动词完成度分析
    const completionVerbs = ['delivered', 'completed', 'finished', 'signed', '完成', '签收', '送达']
    if (verbs.some(verb => completionVerbs.some(cv => verb.toLowerCase().includes(cv)))) {
      semanticBonus += 8
    }

    // 进行时态加成
    if (tenseAnalysis.isProgressive) {
      semanticBonus += 5
    }

    // 过去时态加成（表示已完成）
    if (tenseAnalysis.isPast) {
      semanticBonus += 6
    }

    // 地点信息加成
    if (places.length > 0) {
      semanticBonus += 3
    }

    // 人员信息加成
    if (people.length > 0) {
      semanticBonus += 2
    }

    // 情感极性加成
    semanticBonus += sentiment.polarity * 3

    return {
      semanticScore: Math.max(-10, Math.min(10, semanticBonus)),
      verbs,
      nouns,
      adjectives,
      places,
      people,
      sentiment,
      tenseAnalysis,
      method: 'compromise'
    }
  } catch (error) {
    console.warn('Compromise分析失败:', error)
    return {
      semanticScore: 0,
      method: 'compromise_error',
      error: error.message
    }
  }
}

/**
 * 上下文连贯性分析
 * @param {string} context - 文本内容
 * @returns {Object} 连贯性分析结果
 */
function contextCoherenceAnalysis(context) {
  let coherenceScore = 0
  const features = {
    hasTimeLogic: false,
    hasCausal: false,
    hasLocation: false,
    hasSequence: false
  }

  // 检查时间逻辑词
  TIME_LOGIC_KEYWORDS.forEach(word => {
    if (context.includes(word)) {
      coherenceScore += 2
      features.hasTimeLogic = true
    }
  })

  // 检查因果关系词
  CAUSAL_KEYWORDS.forEach(word => {
    if (context.includes(word)) {
      coherenceScore += 3
      features.hasCausal = true
    }
  })

  // 检查地点连续性
  LOCATION_KEYWORDS.forEach(word => {
    if (context.includes(word)) {
      coherenceScore += 2
      features.hasLocation = true
    }
  })

  // 检查序列词
  const sequenceWords = ['第一', '第二', '首先', '其次', '最后', '最终']
  sequenceWords.forEach(word => {
    if (context.includes(word)) {
      coherenceScore += 2
      features.hasSequence = true
    }
  })

  // 检查逻辑连接词
  const logicWords = ['并且', '而且', '但是', '然而', '不过', '同时']
  logicWords.forEach(word => {
    if (context.includes(word)) {
      coherenceScore += 1
    }
  })

  return {
    coherenceScore: Math.min(coherenceScore, 8),
    features,
    method: 'coherence'
  }
}

/**
 * 时间语义分析
 * @param {string} context - 文本内容
 * @returns {Object} 时间语义结果
 */
function temporalSemanticAnalysis(context) {
  let temporalScore = 0
  const features = {
    hasPastIndicators: false,
    hasPresentIndicators: false,
    hasFutureIndicators: false,
    hasTimeSpecific: false
  }

  // 过去时间指示词
  const pastIndicators = ['已经', '刚刚', '刚才', '之前', '昨天', '前天', '上午', '下午']
  pastIndicators.forEach(word => {
    if (context.includes(word)) {
      temporalScore += 3
      features.hasPastIndicators = true
    }
  })

  // 现在时间指示词
  const presentIndicators = ['现在', '目前', '当前', '正在', '此时', '此刻']
  presentIndicators.forEach(word => {
    if (context.includes(word)) {
      temporalScore += 2
      features.hasPresentIndicators = true
    }
  })

  // 未来时间指示词
  const futureIndicators = ['即将', '准备', '计划', '预计', '将要', '马上']
  futureIndicators.forEach(word => {
    if (context.includes(word)) {
      temporalScore += 1
      features.hasFutureIndicators = true
    }
  })

  // 具体时间表达
  const timePatterns = [
    /\d{1,2}:\d{2}/, // 时间格式 HH:MM
    /\d{1,2}月\d{1,2}日/, // 日期格式
    /\d{4}-\d{1,2}-\d{1,2}/, // 日期格式 YYYY-MM-DD
    /\d+分钟/, // 分钟
    /\d+小时/, // 小时
    /\d+天/ // 天数
  ]

  timePatterns.forEach(pattern => {
    if (pattern.test(context)) {
      temporalScore += 2
      features.hasTimeSpecific = true
    }
  })

  return {
    temporalScore: Math.min(temporalScore, 10),
    features,
    method: 'temporal'
  }
}

/**
 * 分析时态模式
 * @param {Object} doc - compromise文档对象
 * @returns {Object} 时态分析结果
 */
function analyzeTensePattern(doc) {
  const verbs = doc.verbs()

  // 检查是否有过去时态
  const pastVerbs = verbs.toPastTense().out('array')
  const isPast = pastVerbs.length > 0

  // 检查是否有进行时态
  const progressiveVerbs = verbs.toGerund().out('array')
  const isProgressive = progressiveVerbs.length > 0 || doc.has('#Gerund')

  // 检查是否有完成时态
  const perfectVerbs = verbs.conjugate().map(v => v.PastTense).filter(Boolean)
  const isPerfect = perfectVerbs.length > 0

  return {
    isPast,
    isProgressive,
    isPerfect,
    dominantTense: isPast ? 'past' : isProgressive ? 'progressive' : 'present'
  }
}

/**
 * 使用compromise进行情感分析
 * @param {Object} doc - compromise文档对象
 * @returns {Object} 情感分析结果
 */
function analyzeSentimentWithCompromise(doc) {
  // 提取形容词进行情感分析
  const adjectives = doc.adjectives().out('array')

  // 定义情感词汇
  const positiveWords = ['successful', 'completed', 'delivered', 'good', 'normal', '成功', '完成', '正常', '顺利']
  const negativeWords = ['failed', 'error', 'problem', 'delayed', 'unable', '失败', '错误', '延误', '异常']

  let positiveScore = 0
  let negativeScore = 0

  // 分析形容词情感倾向
  adjectives.forEach(adj => {
    const lowerAdj = adj.toLowerCase()
    if (positiveWords.some(word => lowerAdj.includes(word))) {
      positiveScore++
    }
    if (negativeWords.some(word => lowerAdj.includes(word))) {
      negativeScore++
    }
  })

  // 分析整体文本情感
  const text = doc.out('text').toLowerCase()
  positiveWords.forEach(word => {
    if (text.includes(word)) positiveScore++
  })
  negativeWords.forEach(word => {
    if (text.includes(word)) negativeScore++
  })

  const polarity = positiveScore - negativeScore
  const confidence = Math.abs(polarity) / Math.max(positiveScore + negativeScore, 1)

  return {
    positive: positiveScore,
    negative: negativeScore,
    polarity,
    confidence
  }
}

/**
 * 合并多个语义分析结果
 * @param {Array} results - 分析结果数组
 * @param {string} context - 原始文本
 * @returns {Object} 合并后的结果
 */
function mergeSemanticResults(results, context) {
  if (results.length === 0) {
    return {
      semanticScore: 0,
      intensity: 'normal',
      method: 'no_results',
      error: '所有语义分析都失败了'
    }
  }

  // 计算加权平均语义分数
  const weights = {
    basic: 0.4,
    compromise: 0.3,
    coherence: 0.2,
    temporal: 0.1
  }

  let totalScore = 0
  let totalWeight = 0
  let maxIntensity = 'normal'
  const allFeatures = {}

  results.forEach(result => {
    const weight = weights[result.method] || 0.1
    const score = result.semanticScore || result.coherenceScore || result.temporalScore || 0
    
    totalScore += score * weight
    totalWeight += weight

    if (result.intensity === 'high') {
      maxIntensity = 'high'
    }

    // 合并特征
    if (result.features) {
      Object.assign(allFeatures, result.features)
    }
  })

  const finalScore = totalWeight > 0 ? totalScore / totalWeight : 0

  return {
    semanticScore: Math.round(finalScore * 100) / 100,
    intensity: maxIntensity,
    features: allFeatures,
    method: 'merged',
    individualResults: results,
    contextLength: context.length
  }
}

/**
 * 基于compromise分析结果预测物流状态
 * @param {Object} semanticResult - 语义分析结果
 * @param {string} context - 原始上下文
 * @returns {Object|null} 预测结果
 */
export function predictStatusFromSemantic(semanticResult, context) {
  if (!semanticResult || semanticResult.semanticScore === 0) {
    return null
  }

  const { features, semanticScore, intensity } = semanticResult
  let predictedStatus = null
  let confidence = 0.5

  // 基于语义分数预测
  if (semanticScore > 5 && features.hasStrongPositive) {
    predictedStatus = LOGISTICS_STATUS.DELIVERED
    confidence = 0.8
  } else if (semanticScore < -5 && features.hasStrongNegative) {
    predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
    confidence = 0.8
  } else if (features.hasTimeLogic && features.hasLocation) {
    predictedStatus = LOGISTICS_STATUS.IN_TRANSIT
    confidence = 0.6
  } else if (features.hasPresentIndicators) {
    predictedStatus = LOGISTICS_STATUS.OUT_FOR_DELIVERY
    confidence = 0.6
  }

  // 基于强度调整置信度
  if (intensity === 'high') {
    confidence = Math.min(confidence + 0.1, 1)
  }

  return predictedStatus ? {
    status: predictedStatus,
    confidence,
    source: 'semantic'
  } : null
}
