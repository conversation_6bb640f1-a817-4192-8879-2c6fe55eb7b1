# 物流状态分析器 - 新版本

## 概述

这是物流状态分析器的全新模块化版本，提供了更强大的AI分析能力、更丰富的中文关键词库和更好的性能。

## 🚀 新版本特性

### 1. 大幅扩充的中文关键词库
- 移除了所有英文关键词，专注于中文物流场景
- 每个状态的关键词数量增加了3-5倍
- 支持正则表达式匹配和智能权重分级

### 2. AI增强分析
- 集成TensorFlow.js进行深度学习分析
- 支持Natural库进行自然语言处理
- 集成Jieba和node-segment进行中文分词
- 完善的AI加载失败兜底机制

### 3. 高性能优化
- 智能缓存机制，避免重复计算
- 批处理支持，提升大量数据处理效率
- 懒加载AI库，减少初始化时间
- 性能监控装饰器（开发环境）

### 4. 模块化架构
- 清晰的职责分离
- 易于维护和扩展
- 支持按需导入

## 📦 需要安装的依赖

```bash
# AI分析库
npm install @tensorflow/tfjs @tensorflow/tfjs-node

# 自然语言处理
npm install natural

# 语义分析（已有）
npm install compromise

# 中文分词
npm install jieba-js node-segment
```

## 📁 模块结构

```
src/utils/logistics/
├── index.js                    # 主入口文件，统一导出
├── constants.js               # 常量定义（状态枚举、配置等）
├── keywords.js                # 扩充的中文关键词配置
├── analyzer.js                # 核心分析器，整合所有分析方法
├── aiAnalyzer.js              # AI分析模块（TensorFlow、Natural等）
├── semanticAnalyzer.js        # 语义分析模块（compromise等）
├── statusTransition.js        # 状态转换逻辑和验证
├── utils.js                   # 工具函数和性能优化
└── README.md                  # 本文档
```

## 🔧 使用方法

### 基础使用

```javascript
import { analyzeLogisticsStatus } from '@/utils/logistics'

// 分析物流轨迹
const result = await analyzeLogisticsStatus(orderTrack, {
  enableAI: true,           // 启用AI分析
  enableCache: true,        // 启用缓存
  maxAnalysisDepth: 5,      // 最大分析深度
  confidenceThreshold: 0.5  // 置信度阈值
})

console.log(result)
// {
//   status: 'delivered',
//   statusText: '已签收',
//   confidence: 0.95,
//   urgency: 1,
//   recommendations: [],
//   analysisMethod: 'ai_enhanced'
// }
```

### 详细分析

```javascript
import { getDetailedLogisticsAnalysis } from '@/utils/logistics'

const detailedResult = await getDetailedLogisticsAnalysis(orderTrack)
console.log(detailedResult)
// 包含更多分析细节，如各种分析方法的结果、状态转换验证等
```

### 快速检测

```javascript
import { quickStatusDetection } from '@/utils/logistics'

// 仅分析最新记录，速度更快
const quickResult = await quickStatusDetection('已签收', trackRecord)
```

### 批量分析

```javascript
import { batchAnalyzeOrders } from '@/utils/logistics'

const orders = [
  { orderId: '001', orderTrack: [...] },
  { orderId: '002', orderTrack: [...] }
]

const batchResults = await batchAnalyzeOrders(orders, {
  enableAI: true
})
```

### AI功能检查

```javascript
import { checkAIAvailability } from '@/utils/logistics'

const aiStatus = await checkAIAvailability()
console.log(aiStatus)
// {
//   available: true,
//   models: {
//     tensorflow: true,
//     natural: true,
//     jieba: true,
//     segment: true
//   }
// }
```

### 系统健康检查

```javascript
import { getSystemHealth } from '@/utils/logistics'

const health = await getSystemHealth()
console.log(health)
// {
//   status: 'healthy',
//   ai: { available: true },
//   cache: { enabled: true, size: 150 },
//   performance: { cacheEnabled: true, aiEnabled: true }
// }
```

## ⚙️ 配置选项

### AI配置

```javascript
import { configureAnalyzer } from '@/utils/logistics'

configureAnalyzer({
  ai: {
    TENSORFLOW_MODEL_URL: '/custom/model/path',
    CONFIDENCE_THRESHOLD: 0.8,
    TIMEOUT_MS: 5000,
    MAX_RETRIES: 3
  }
})
```

### 性能配置

```javascript
configureAnalyzer({
  performance: {
    MAX_TRACK_ANALYSIS: 10,    // 最多分析的轨迹条数
    CACHE_ENABLED: true,       // 启用缓存
    CACHE_TTL: 600000,        // 缓存10分钟
    BATCH_SIZE: 20            // 批处理大小
  }
})
```

## 🔍 错误处理

系统具有完善的错误处理机制：

1. **AI加载失败**: 自动降级到传统分析方法
2. **网络超时**: 使用本地缓存或兜底逻辑
3. **数据格式错误**: 提供默认状态和建议
4. **依赖库缺失**: 优雅降级，不影响核心功能

## 📊 性能优化

1. **智能缓存**: 避免重复计算相同的轨迹
2. **懒加载**: AI库按需加载，减少初始化时间
3. **批处理**: 大量数据处理时自动分批
4. **权重优化**: 根据文本长度动态调整关键词权重

## 🧪 测试建议

建议编写以下测试用例：

```javascript
import { analyzeLogisticsStatus } from '@/utils/logistics'

// 测试基本功能
test('基本状态分析', async () => {
  const orderTrack = [
    { context: '已签收', msgTime: new Date().toISOString() }
  ]
  const result = await analyzeLogisticsStatus(orderTrack)
  expect(result.status).toBe('delivered')
  expect(result.confidence).toBeGreaterThan(0.8)
})

// 测试AI功能
test('AI分析功能', async () => {
  const result = await analyzeLogisticsStatus(orderTrack, { enableAI: true })
  expect(result.aiAnalysis).toBeDefined()
})

// 测试错误处理
test('空数据处理', async () => {
  const result = await analyzeLogisticsStatus([])
  expect(result.status).toBe('pending_pickup')
  expect(result.confidence).toBe(0)
})
```

## 🔄 向后兼容

原有的 `src/utils/logisticsStatusAnalyzer.js` 文件仍然可用，它会自动重新导出新模块的功能，确保现有代码无需修改即可享受新版本的所有改进。

## 📈 未来规划

1. 支持更多AI模型（如BERT、GPT等）
2. 增加多语言支持
3. 实时学习和模型更新
4. 更精细的异常检测
5. 可视化分析报告
