/**
 * 物流状态分析器 - AI分析模块
 * 集成TensorFlow.js和自然语言处理库进行智能分析
 * 包含完善的错误处理和兜底机制
 */

import { LOGISTICS_STATUS, AI_CONFIG, CONFIDENCE_THRESHOLDS } from './constants.js'

// AI模型和库的懒加载状态
let tfModel = null
let naturalLib = null
let jiebaLib = null
let segmentLib = null
let isLoading = false
let loadError = null

/**
 * 懒加载AI依赖库
 * @returns {Promise<Object>} 加载结果
 */
async function loadAIDependencies() {
  if (isLoading) {
    // 等待正在进行的加载
    while (isLoading) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    return { success: !loadError, error: loadError }
  }

  if (tfModel && naturalLib && jiebaLib && segmentLib) {
    return { success: true }
  }

  isLoading = true
  loadError = null

  try {
    // 动态导入AI库
    const [tf, natural, jieba, Segment] = await Promise.all([
      import('@tensorflow/tfjs').catch(() => null),
      import('natural').catch(() => null),
      import('jieba-js').catch(() => null),
      import('node-segment').catch(() => null)
    ])

    if (tf) {
      // 尝试加载预训练模型
      try {
        tfModel = await tf.loadLayersModel(AI_CONFIG.TENSORFLOW_MODEL_URL)
      } catch (error) {
        console.warn('TensorFlow模型加载失败，将使用传统方法:', error.message)
        tfModel = null
      }
    }

    naturalLib = natural
    jiebaLib = jieba
    segmentLib = Segment ? new Segment.Segment() : null

    // 初始化中文分词器
    if (segmentLib) {
      segmentLib.useDefault()
    }

    return { success: true }
  } catch (error) {
    loadError = error
    console.warn('AI依赖库加载失败，将使用传统分析方法:', error.message)
    return { success: false, error }
  } finally {
    isLoading = false
  }
}

/**
 * AI增强的物流状态分析
 * @param {string} context - 轨迹描述文本
 * @param {Object} trackRecord - 完整轨迹记录
 * @returns {Promise<Object>} AI分析结果
 */
export async function analyzeWithAI(context, trackRecord = {}) {
  const startTime = Date.now()
  
  try {
    // 设置超时
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('AI分析超时')), AI_CONFIG.TIMEOUT_MS)
    })

    const analysisPromise = performAIAnalysis(context, trackRecord)
    
    const result = await Promise.race([analysisPromise, timeoutPromise])
    
    return {
      ...result,
      analysisTime: Date.now() - startTime,
      source: 'ai'
    }
  } catch (error) {
    console.warn('AI分析失败，使用兜底方案:', error.message)
    return getFallbackAnalysis(context, trackRecord)
  }
}

/**
 * 执行AI分析
 * @param {string} context - 轨迹描述文本
 * @param {Object} trackRecord - 完整轨迹记录
 * @returns {Promise<Object>} 分析结果
 */
async function performAIAnalysis(context, trackRecord) {
  const loadResult = await loadAIDependencies()
  
  if (!loadResult.success) {
    throw new Error('AI依赖库加载失败')
  }

  // 多维度AI分析
  const analyses = await Promise.allSettled([
    tensorflowAnalysis(context),
    naturalLanguageAnalysis(context),
    chineseSegmentationAnalysis(context),
    semanticSimilarityAnalysis(context)
  ])

  // 合并分析结果
  const results = analyses
    .filter(result => result.status === 'fulfilled')
    .map(result => result.value)

  if (results.length === 0) {
    throw new Error('所有AI分析方法都失败了')
  }

  // 加权融合多个AI分析结果
  return fuseAIResults(results, context, trackRecord)
}

/**
 * TensorFlow.js模型分析
 * @param {string} context - 文本内容
 * @returns {Promise<Object>} 分析结果
 */
async function tensorflowAnalysis(context) {
  if (!tfModel) {
    throw new Error('TensorFlow模型未加载')
  }

  try {
    // 文本预处理和特征提取
    const features = extractTextFeatures(context)
    const tensor = tfModel.tf.tensor2d([features])
    
    // 模型预测
    const prediction = tfModel.predict(tensor)
    const probabilities = await prediction.data()
    
    // 清理张量
    tensor.dispose()
    prediction.dispose()

    // 找到最高概率的状态
    const maxIndex = probabilities.indexOf(Math.max(...probabilities))
    const statusMap = Object.values(LOGISTICS_STATUS)
    
    return {
      status: statusMap[maxIndex] || LOGISTICS_STATUS.PENDING_PICKUP,
      confidence: probabilities[maxIndex],
      method: 'tensorflow',
      probabilities: Object.fromEntries(
        statusMap.map((status, i) => [status, probabilities[i]])
      )
    }
  } catch (error) {
    throw new Error(`TensorFlow分析失败: ${error.message}`)
  }
}

/**
 * Natural库自然语言分析
 * @param {string} context - 文本内容
 * @returns {Promise<Object>} 分析结果
 */
async function naturalLanguageAnalysis(context) {
  if (!naturalLib) {
    throw new Error('Natural库未加载')
  }

  try {
    // 情感分析
    const sentiment = naturalLib.SentimentAnalyzer.analyze(
      naturalLib.WordTokenizer().tokenize(context)
    )

    // 词干提取
    const stemmer = naturalLib.PorterStemmer
    const tokens = naturalLib.WordTokenizer().tokenize(context)
    const stems = tokens.map(token => stemmer.stem(token))

    // 基于情感和关键词推断状态
    let predictedStatus = LOGISTICS_STATUS.PENDING_PICKUP
    let confidence = 0.5

    if (sentiment > 0.5) {
      predictedStatus = LOGISTICS_STATUS.DELIVERED
      confidence = 0.7 + sentiment * 0.2
    } else if (sentiment < -0.5) {
      predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
      confidence = 0.7 + Math.abs(sentiment) * 0.2
    }

    return {
      status: predictedStatus,
      confidence: Math.min(confidence, 1),
      method: 'natural',
      sentiment,
      tokens,
      stems
    }
  } catch (error) {
    throw new Error(`Natural分析失败: ${error.message}`)
  }
}

/**
 * 中文分词分析
 * @param {string} context - 文本内容
 * @returns {Promise<Object>} 分析结果
 */
async function chineseSegmentationAnalysis(context) {
  if (!jiebaLib && !segmentLib) {
    throw new Error('中文分词库未加载')
  }

  try {
    let segments = []
    
    // 优先使用jieba分词
    if (jiebaLib) {
      segments = jiebaLib.cut(context)
    } else if (segmentLib) {
      segments = segmentLib.doSegment(context).map(item => item.w)
    }

    // 基于分词结果进行状态分析
    const statusKeywords = {
      [LOGISTICS_STATUS.DELIVERED]: ['签收', '完成', '送达', '妥投'],
      [LOGISTICS_STATUS.IN_TRANSIT]: ['运输', '途中', '转运', '发往'],
      [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: ['派送', '配送', '投递'],
      [LOGISTICS_STATUS.FAILED_DELIVERY]: ['失败', '拒收', '异常'],
      [LOGISTICS_STATUS.PICKED_UP]: ['揽收', '收件', '取件'],
      [LOGISTICS_STATUS.RETURNED]: ['退回', '返回', '退件'],
      [LOGISTICS_STATUS.EXCEPTION]: ['异常', '问题', '丢失', '破损']
    }

    let bestStatus = LOGISTICS_STATUS.PENDING_PICKUP
    let maxScore = 0

    for (const [status, keywords] of Object.entries(statusKeywords)) {
      const score = keywords.reduce((sum, keyword) => {
        return sum + segments.filter(seg => seg.includes(keyword)).length
      }, 0)

      if (score > maxScore) {
        maxScore = score
        bestStatus = status
      }
    }

    const confidence = Math.min(maxScore / segments.length * 2, 1)

    return {
      status: bestStatus,
      confidence: Math.max(confidence, 0.3),
      method: 'chinese_segmentation',
      segments,
      matchScore: maxScore
    }
  } catch (error) {
    throw new Error(`中文分词分析失败: ${error.message}`)
  }
}

/**
 * 语义相似度分析
 * @param {string} context - 文本内容
 * @returns {Promise<Object>} 分析结果
 */
async function semanticSimilarityAnalysis(context) {
  if (!naturalLib) {
    throw new Error('语义分析库未加载')
  }

  try {
    // 预定义的状态模板
    const statusTemplates = {
      [LOGISTICS_STATUS.DELIVERED]: '已签收 投递成功 派送完成 客户收货',
      [LOGISTICS_STATUS.IN_TRANSIT]: '运输中 在途 转运 发往目的地',
      [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: '派送中 配送员派送 正在投递',
      [LOGISTICS_STATUS.FAILED_DELIVERY]: '派送失败 无人签收 拒收',
      [LOGISTICS_STATUS.PICKED_UP]: '已揽收 快递员收件 取件成功',
      [LOGISTICS_STATUS.RETURNED]: '已退回 返回发件人 退件',
      [LOGISTICS_STATUS.EXCEPTION]: '异常 问题件 丢失 破损'
    }

    let bestStatus = LOGISTICS_STATUS.PENDING_PICKUP
    let maxSimilarity = 0

    // 计算与各状态模板的相似度
    for (const [status, template] of Object.entries(statusTemplates)) {
      const similarity = naturalLib.JaroWinklerDistance(
        context.toLowerCase(),
        template.toLowerCase()
      )

      if (similarity > maxSimilarity) {
        maxSimilarity = similarity
        bestStatus = status
      }
    }

    return {
      status: bestStatus,
      confidence: maxSimilarity,
      method: 'semantic_similarity',
      similarity: maxSimilarity
    }
  } catch (error) {
    throw new Error(`语义相似度分析失败: ${error.message}`)
  }
}

/**
 * 融合多个AI分析结果
 * @param {Array} results - AI分析结果数组
 * @param {string} context - 原始文本
 * @param {Object} trackRecord - 轨迹记录
 * @returns {Object} 融合后的结果
 */
function fuseAIResults(results, context, trackRecord) {
  if (results.length === 0) {
    throw new Error('没有有效的AI分析结果')
  }

  // 加权投票
  const weights = {
    tensorflow: 0.4,
    natural: 0.2,
    chinese_segmentation: 0.3,
    semantic_similarity: 0.1
  }

  const statusVotes = {}
  let totalWeight = 0

  results.forEach(result => {
    const weight = weights[result.method] || 0.1
    const vote = result.confidence * weight
    
    statusVotes[result.status] = (statusVotes[result.status] || 0) + vote
    totalWeight += weight
  })

  // 找到得票最高的状态
  const bestStatus = Object.keys(statusVotes).reduce((a, b) => 
    statusVotes[a] > statusVotes[b] ? a : b
  )

  const confidence = statusVotes[bestStatus] / totalWeight

  return {
    status: bestStatus,
    confidence: Math.min(confidence, 1),
    method: 'ai_fusion',
    individualResults: results,
    statusVotes,
    aiEnabled: true
  }
}

/**
 * 提取文本特征用于TensorFlow模型
 * @param {string} text - 文本内容
 * @returns {Array} 特征向量
 */
function extractTextFeatures(text) {
  // 简化的特征提取，实际应用中需要更复杂的特征工程
  const features = new Array(100).fill(0) // 假设模型需要100维特征
  
  // 文本长度特征
  features[0] = Math.min(text.length / 100, 1)
  
  // 关键词特征（简化版）
  const keywords = ['签收', '派送', '运输', '异常', '失败', '完成']
  keywords.forEach((keyword, index) => {
    if (index < 50) {
      features[index + 1] = text.includes(keyword) ? 1 : 0
    }
  })

  return features
}

/**
 * 兜底分析方案
 * @param {string} context - 文本内容
 * @param {Object} trackRecord - 轨迹记录
 * @returns {Object} 兜底分析结果
 */
function getFallbackAnalysis(context, trackRecord) {
  // 简单的规则基础分析作为兜底
  const lowerContext = context.toLowerCase()
  
  if (lowerContext.includes('签收') || lowerContext.includes('完成')) {
    return {
      status: LOGISTICS_STATUS.DELIVERED,
      confidence: 0.6,
      method: 'fallback_rule',
      aiEnabled: false,
      fallbackReason: 'AI分析失败，使用规则兜底'
    }
  }
  
  if (lowerContext.includes('派送') || lowerContext.includes('配送')) {
    return {
      status: LOGISTICS_STATUS.OUT_FOR_DELIVERY,
      confidence: 0.5,
      method: 'fallback_rule',
      aiEnabled: false,
      fallbackReason: 'AI分析失败，使用规则兜底'
    }
  }

  return {
    status: LOGISTICS_STATUS.PENDING_PICKUP,
    confidence: 0.3,
    method: 'fallback_default',
    aiEnabled: false,
    fallbackReason: 'AI分析失败，使用默认状态'
  }
}

/**
 * 检查AI功能是否可用
 * @returns {Promise<Object>} 可用性检查结果
 */
export async function checkAIAvailability() {
  const result = await loadAIDependencies()
  return {
    available: result.success,
    error: result.error?.message,
    models: {
      tensorflow: !!tfModel,
      natural: !!naturalLib,
      jieba: !!jiebaLib,
      segment: !!segmentLib
    }
  }
}
