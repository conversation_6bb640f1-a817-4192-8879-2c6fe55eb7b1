/**
 * 物流状态分析器 - 状态转换逻辑模块
 * 处理物流状态的转换验证和推理
 */

import { LOGISTICS_STATUS, FINAL_STATUSES } from './constants.js'

// 状态转换规则映射
const STATUS_TRANSITION_RULES = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: {
    next: [LOGISTICS_STATUS.PICKED_UP, LOGISTICS_STATUS.EXCEPTION],
    probability: { [LOGISTICS_STATUS.PICKED_UP]: 0.9, [LOGISTICS_STATUS.EXCEPTION]: 0.1 }
  },
  [LOGISTICS_STATUS.PICKED_UP]: {
    next: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.EXCEPTION],
    probability: { 
      [LOGISTICS_STATUS.IN_TRANSIT]: 0.8, 
      [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 0.15,
      [LOGISTICS_STATUS.EXCEPTION]: 0.05 
    }
  },
  [LOGISTICS_STATUS.IN_TRANSIT]: {
    next: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.EXCEPTION],
    probability: { 
      [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 0.85, 
      [LOGISTICS_STATUS.DELIVERED]: 0.1,
      [LOGISTICS_STATUS.EXCEPTION]: 0.05 
    }
  },
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: {
    next: [LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.FAILED_DELIVERY, LOGISTICS_STATUS.EXCEPTION],
    probability: { 
      [LOGISTICS_STATUS.DELIVERED]: 0.8, 
      [LOGISTICS_STATUS.FAILED_DELIVERY]: 0.15,
      [LOGISTICS_STATUS.EXCEPTION]: 0.05 
    }
  },
  [LOGISTICS_STATUS.FAILED_DELIVERY]: {
    next: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED, LOGISTICS_STATUS.EXCEPTION],
    probability: { 
      [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 0.6, 
      [LOGISTICS_STATUS.RETURNED]: 0.3,
      [LOGISTICS_STATUS.EXCEPTION]: 0.1 
    }
  },
  [LOGISTICS_STATUS.DELIVERED]: {
    next: [], // 终态
    probability: {}
  },
  [LOGISTICS_STATUS.RETURNED]: {
    next: [], // 终态
    probability: {}
  },
  [LOGISTICS_STATUS.EXCEPTION]: {
    next: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED],
    probability: { 
      [LOGISTICS_STATUS.IN_TRANSIT]: 0.4, 
      [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 0.4,
      [LOGISTICS_STATUS.RETURNED]: 0.2 
    }
  }
}

/**
 * 获取可能的下一状态
 * @param {string} currentStatus - 当前状态
 * @returns {Array} 可能的下一状态数组
 */
export function getPossibleNextStates(currentStatus) {
  const rule = STATUS_TRANSITION_RULES[currentStatus]
  return rule ? rule.next : []
}

/**
 * 获取状态转换概率
 * @param {string} fromStatus - 起始状态
 * @param {string} toStatus - 目标状态
 * @returns {number} 转换概率 (0-1)
 */
export function getTransitionProbability(fromStatus, toStatus) {
  const rule = STATUS_TRANSITION_RULES[fromStatus]
  if (!rule || !rule.probability) return 0
  return rule.probability[toStatus] || 0
}

/**
 * 验证状态转换的合理性
 * @param {Array} statusSequence - 状态序列（按时间倒序）
 * @returns {Object} 转换验证结果
 */
export function validateStatusTransitions(statusSequence) {
  if (statusSequence.length <= 1) {
    return { 
      isValid: true, 
      confidence: 1, 
      invalidTransitions: [],
      validTransitionCount: 0,
      totalTransitions: 0
    }
  }

  const invalidTransitions = []
  let validTransitionCount = 0

  // 检查相邻状态转换的合理性（注意：statusSequence是倒序的）
  for (let i = 0; i < statusSequence.length - 1; i++) {
    const currentStatus = statusSequence[i] // 较新的状态
    const previousStatus = statusSequence[i + 1] // 较旧的状态

    const allowedNextStates = getPossibleNextStates(previousStatus)
    const isValidTransition = allowedNextStates.includes(currentStatus) || currentStatus === previousStatus

    if (isValidTransition) {
      validTransitionCount++
    } else {
      invalidTransitions.push({
        from: previousStatus,
        to: currentStatus,
        index: i,
        probability: getTransitionProbability(previousStatus, currentStatus)
      })
    }
  }

  const totalTransitions = statusSequence.length - 1
  const validityRatio = totalTransitions > 0 ? validTransitionCount / totalTransitions : 1

  return {
    isValid: validityRatio >= 0.7,
    confidence: validityRatio,
    invalidTransitions,
    validTransitionCount,
    totalTransitions,
    validityRatio
  }
}

/**
 * 分析状态序列的一致性
 * @param {Array} statusSequence - 状态序列
 * @returns {Object} 一致性分析结果
 */
export function analyzeStatusConsistency(statusSequence) {
  if (statusSequence.length <= 1) {
    return { 
      isConsistent: true, 
      confidence: 1, 
      dominantStatus: statusSequence[0] || LOGISTICS_STATUS.PENDING_PICKUP,
      statusDistribution: {}
    }
  }

  // 统计各状态出现频率
  const statusCount = {}
  statusSequence.forEach(status => {
    statusCount[status] = (statusCount[status] || 0) + 1
  })

  const totalCount = statusSequence.length
  const maxCount = Math.max(...Object.values(statusCount))
  const dominantStatus = Object.keys(statusCount).find(status => statusCount[status] === maxCount)

  const consistency = maxCount / totalCount

  // 计算状态分布熵（衡量混乱程度）
  const entropy = calculateStatusEntropy(statusCount, totalCount)

  return {
    isConsistent: consistency >= 0.6,
    confidence: consistency,
    dominantStatus,
    statusDistribution: statusCount,
    entropy,
    diversityScore: Object.keys(statusCount).length / totalCount
  }
}

/**
 * 计算状态分布熵
 * @param {Object} statusCount - 状态计数
 * @param {number} totalCount - 总数
 * @returns {number} 熵值
 */
function calculateStatusEntropy(statusCount, totalCount) {
  let entropy = 0
  for (const count of Object.values(statusCount)) {
    const probability = count / totalCount
    if (probability > 0) {
      entropy -= probability * Math.log2(probability)
    }
  }
  return entropy
}

/**
 * 从历史轨迹推断当前状态
 * @param {Array} trackAnalyses - 轨迹分析结果数组
 * @returns {Object|null} 推断结果
 */
export function inferStatusFromHistory(trackAnalyses) {
  if (trackAnalyses.length < 2) return null

  // 获取最近几条有意义的轨迹
  const meaningfulTracks = trackAnalyses.filter(track => track.confidence > 0.3)

  if (meaningfulTracks.length === 0) return null

  // 找到最近的高置信度状态
  const lastHighConfidenceTrack = meaningfulTracks.find(track => track.confidence >= 0.7)

  if (!lastHighConfidenceTrack) return null

  // 基于最后的高置信度状态推断当前可能的状态
  const lastStatus = lastHighConfidenceTrack.status
  const possibleNextStates = getPossibleNextStates(lastStatus)

  // 如果只有一个可能的下一状态，使用它
  if (possibleNextStates.length === 1) {
    return {
      status: possibleNextStates[0],
      confidence: Math.min(lastHighConfidenceTrack.confidence * 0.8, 0.7),
      inferenceMethod: 'single_next_state',
      baseStatus: lastStatus
    }
  }

  // 如果有多个可能状态，选择最合理的
  const mostLikelyStatus = selectMostLikelyNextStatus(lastStatus, trackAnalyses)

  return mostLikelyStatus ? {
    status: mostLikelyStatus,
    confidence: Math.min(lastHighConfidenceTrack.confidence * 0.7, 0.6),
    inferenceMethod: 'probability_based',
    baseStatus: lastStatus,
    possibleStates: possibleNextStates
  } : null
}

/**
 * 选择最可能的下一状态
 * @param {string} lastStatus - 最后的明确状态
 * @param {Array} trackAnalyses - 轨迹分析结果
 * @returns {string|null} 最可能的状态
 */
export function selectMostLikelyNextStatus(lastStatus, trackAnalyses) {
  const possibleStates = getPossibleNextStates(lastStatus)

  if (possibleStates.length === 0) return lastStatus
  if (possibleStates.length === 1) return possibleStates[0]

  // 基于概率和时间因素选择
  const probabilities = STATUS_TRANSITION_RULES[lastStatus]?.probability || {}
  
  // 考虑时间因素
  const timeFactors = calculateTimeFactors(trackAnalyses)
  
  // 计算加权概率
  let bestStatus = possibleStates[0]
  let maxScore = 0

  possibleStates.forEach(status => {
    const baseProbability = probabilities[status] || 0
    const timeFactor = timeFactors[status] || 1
    const score = baseProbability * timeFactor

    if (score > maxScore) {
      maxScore = score
      bestStatus = status
    }
  })

  return bestStatus
}

/**
 * 计算时间因素对状态转换的影响
 * @param {Array} trackAnalyses - 轨迹分析结果
 * @returns {Object} 时间因素映射
 */
function calculateTimeFactors(trackAnalyses) {
  const timeFactors = {}
  
  if (trackAnalyses.length === 0) return timeFactors

  const latestTrack = trackAnalyses[0]
  const timestamp = latestTrack.timestamp || latestTrack.msgTime || latestTrack.time

  if (!timestamp) return timeFactors

  try {
    const trackTime = new Date(timestamp)
    const now = new Date()
    const hoursDiff = (now - trackTime) / (1000 * 60 * 60)

    // 基于时间差调整状态转换概率
    if (hoursDiff > 48) {
      // 超过48小时，更可能出现异常或完成
      timeFactors[LOGISTICS_STATUS.EXCEPTION] = 1.5
      timeFactors[LOGISTICS_STATUS.DELIVERED] = 1.3
    } else if (hoursDiff > 24) {
      // 超过24小时，正常推进
      timeFactors[LOGISTICS_STATUS.OUT_FOR_DELIVERY] = 1.2
      timeFactors[LOGISTICS_STATUS.DELIVERED] = 1.1
    } else if (hoursDiff < 2) {
      // 2小时内，状态可能还在变化
      timeFactors[LOGISTICS_STATUS.IN_TRANSIT] = 1.2
      timeFactors[LOGISTICS_STATUS.OUT_FOR_DELIVERY] = 1.1
    }

    return timeFactors
  } catch (error) {
    return timeFactors
  }
}

/**
 * 判断是否为终态状态
 * @param {string} status - 物流状态
 * @returns {boolean} 是否为终态
 */
export function isFinalStatus(status) {
  return FINAL_STATUSES.has(status)
}

/**
 * 获取状态转换路径
 * @param {string} fromStatus - 起始状态
 * @param {string} toStatus - 目标状态
 * @returns {Array|null} 转换路径，如果无法到达则返回null
 */
export function getTransitionPath(fromStatus, toStatus) {
  if (fromStatus === toStatus) return [fromStatus]

  const visited = new Set()
  const queue = [[fromStatus]]

  while (queue.length > 0) {
    const path = queue.shift()
    const currentStatus = path[path.length - 1]

    if (currentStatus === toStatus) {
      return path
    }

    if (visited.has(currentStatus)) continue
    visited.add(currentStatus)

    const nextStates = getPossibleNextStates(currentStatus)
    for (const nextStatus of nextStates) {
      if (!visited.has(nextStatus)) {
        queue.push([...path, nextStatus])
      }
    }

    // 防止无限循环，限制路径长度
    if (path.length > 10) break
  }

  return null // 无法到达
}

/**
 * 计算状态转换的可信度
 * @param {Array} statusSequence - 状态序列
 * @param {Array} confidenceSequence - 对应的置信度序列
 * @returns {Object} 转换可信度分析
 */
export function calculateTransitionCredibility(statusSequence, confidenceSequence) {
  if (statusSequence.length !== confidenceSequence.length || statusSequence.length <= 1) {
    return {
      overallCredibility: 0,
      averageConfidence: 0,
      transitionCredibility: []
    }
  }

  const transitionCredibility = []
  let totalCredibility = 0

  for (let i = 0; i < statusSequence.length - 1; i++) {
    const fromStatus = statusSequence[i + 1] // 较旧的状态
    const toStatus = statusSequence[i] // 较新的状态
    const confidence = confidenceSequence[i]

    const transitionProbability = getTransitionProbability(fromStatus, toStatus)
    const credibility = confidence * transitionProbability

    transitionCredibility.push({
      from: fromStatus,
      to: toStatus,
      confidence,
      transitionProbability,
      credibility
    })

    totalCredibility += credibility
  }

  const averageConfidence = confidenceSequence.reduce((sum, conf) => sum + conf, 0) / confidenceSequence.length
  const overallCredibility = totalCredibility / transitionCredibility.length

  return {
    overallCredibility,
    averageConfidence,
    transitionCredibility,
    isHighCredibility: overallCredibility > 0.7
  }
}
