/**
 * 物流状态分析器 - 主入口文件
 * 提供统一的API接口，整合所有功能模块
 * 支持AI增强分析、高性能缓存、完善的错误处理
 */

// 导出常量
export {
  LOGISTICS_STATUS,
  STATUS_TEXT_MAP,
  STATUS_URGENCY_MAP,
  FINAL_STATUSES,
  ANALYSIS_METHODS,
  AI_CONFIG,
  PERFORMANCE_CONFIG,
  CONFIDENCE_THRESHOLDS
} from './constants.js'

// 导出核心分析功能
export { analyzeLogisticsStatus, analyzeTrackContext } from './analyzer.js'

// 导出AI分析功能
export { analyzeWithAI, checkAIAvailability } from './aiAnalyzer.js'

// 导出语义分析功能
export { enhancedSemanticAnalysis, predictStatusFromSemantic } from './semanticAnalyzer.js'

// 导出状态转换功能
export {
  getPossibleNextStates,
  getTransitionProbability,
  validateStatusTransitions,
  analyzeStatusConsistency,
  inferStatusFromHistory,
  isFinalStatus,
  selectMostLikelyNextStatus,
  getTransitionPath,
  calculateTransitionCredibility
} from './statusTransition.js'

// 导出工具函数
export {
  calculateKeywordScore,
  calculateMatchQuality,
  isAmbiguousContext,
  analyzeTimeFactors,
  getStatusUrgency,
  generateRecommendations,
  formatAnalysisResult,
  batchAnalyzeTrackRecords,
  clearAllCache,
  getCacheStats,
  preprocessText,
  calculateTextSimilarity,
  withPerformanceMonitoring
} from './utils.js'

// 导出关键词配置
export {
  STATUS_KEYWORDS,
  AMBIGUOUS_KEYWORDS,
  STRONG_POSITIVE_KEYWORDS,
  STRONG_NEGATIVE_KEYWORDS,
  TIME_LOGIC_KEYWORDS,
  CAUSAL_KEYWORDS,
  LOCATION_KEYWORDS
} from './keywords.js'

/**
 * 获取详细的物流状态分析报告
 * @param {Array} orderTrack - 物流轨迹数组
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 详细分析报告
 */
export async function getDetailedLogisticsAnalysis(orderTrack, options = {}) {
  // 避免循环引用，直接导入需要的模块
  const analyzerModule = await import('./analyzer.js')
  const utilsModule = await import('./utils.js')
  const statusModule = await import('./statusTransition.js')

  const basicAnalysis = await analyzerModule.analyzeLogisticsStatus(orderTrack, options)

  return {
    ...basicAnalysis,
    urgency: utilsModule.getStatusUrgency(basicAnalysis.status),
    isFinal: statusModule.isFinalStatus(basicAnalysis.status),
    recommendations: utilsModule.generateRecommendations(basicAnalysis),
    trackCount: orderTrack?.length || 0,
    analysisTimestamp: new Date().toISOString(),
    analysisOptions: options
  }
}

/**
 * 快速状态检测 - 仅分析最新记录
 * @param {string} latestContext - 最新轨迹描述
 * @param {Object} trackRecord - 轨迹记录
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 快速分析结果
 */
export async function quickStatusDetection(latestContext, trackRecord = {}, options = {}) {
  try {
    // 避免循环引用，使用简化的分析逻辑
    const { STATUS_KEYWORDS } = await import('./keywords.js')
    const { calculateKeywordScore, formatAnalysisResult } = await import('./utils.js')
    const { LOGISTICS_STATUS, STATUS_TEXT_MAP } = await import('./constants.js')

    let maxScore = 0
    let detectedStatus = LOGISTICS_STATUS.PENDING_PICKUP

    // 简化的关键词匹配
    for (const [status, keywords] of Object.entries(STATUS_KEYWORDS)) {
      const score = calculateKeywordScore(latestContext, keywords)
      if (score > maxScore) {
        maxScore = score
        detectedStatus = status
      }
    }

    const confidence = Math.min(maxScore / 50, 1)

    return formatAnalysisResult({
      status: detectedStatus,
      statusText: STATUS_TEXT_MAP[detectedStatus],
      confidence: Math.max(confidence, 0.1),
      analysisMethod: 'quick_detection',
      isQuickAnalysis: true
    })
  } catch (error) {
    console.warn('快速状态检测失败:', error)
    return {
      status: 'pending_pickup',
      statusText: '待揽件',
      confidence: 0.1,
      error: error.message,
      analysisMethod: 'quick_detection_fallback'
    }
  }
}

/**
 * 批量分析多个订单的物流状态
 * @param {Array} orders - 订单数组，每个订单包含orderTrack字段
 * @param {Object} options - 分析选项
 * @returns {Promise<Array>} 批量分析结果
 */
export async function batchAnalyzeOrders(orders, options = {}) {
  const results = []

  for (const order of orders) {
    try {
      const analysis = await getDetailedLogisticsAnalysis(order.orderTrack || [], options)
      results.push({
        orderId: order.orderId || order.id,
        ...analysis
      })
    } catch (error) {
      results.push({
        orderId: order.orderId || order.id,
        error: error.message,
        status: 'pending_pickup',
        confidence: 0
      })
    }
  }

  return results
}

/**
 * 状态变化检测 - 比较两次分析结果
 * @param {Object} previousAnalysis - 之前的分析结果
 * @param {Object} currentAnalysis - 当前的分析结果
 * @returns {Object} 变化检测结果
 */
export function detectStatusChange(previousAnalysis, currentAnalysis) {
  const hasStatusChanged = previousAnalysis.status !== currentAnalysis.status
  const confidenceChange = currentAnalysis.confidence - previousAnalysis.confidence

  return {
    hasChanged: hasStatusChanged,
    previousStatus: previousAnalysis.status,
    currentStatus: currentAnalysis.status,
    statusChange: hasStatusChanged ? {
      from: previousAnalysis.status,
      to: currentAnalysis.status,
      fromText: previousAnalysis.statusText,
      toText: currentAnalysis.statusText
    } : null,
    confidenceChange,
    confidenceImproved: confidenceChange > 0.1,
    confidenceDegraded: confidenceChange < -0.1,
    analysisTimestamp: new Date().toISOString()
  }
}

/**
 * 获取系统健康状态
 * @returns {Promise<Object>} 系统状态信息
 */
export async function getSystemHealth() {
  try {
    const aiModule = await import('./aiAnalyzer.js')
    const utilsModule = await import('./utils.js')

    const [aiStatus, cacheStats] = await Promise.all([
      aiModule.checkAIAvailability(),
      Promise.resolve(utilsModule.getCacheStats())
    ])

    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      ai: aiStatus,
      cache: cacheStats,
      performance: {
        cacheEnabled: cacheStats.enabled,
        aiEnabled: aiStatus.available
      }
    }
  } catch (error) {
    return {
      status: 'degraded',
      timestamp: new Date().toISOString(),
      error: error.message,
      ai: { available: false, error: error.message },
      cache: { enabled: false },
      performance: {
        cacheEnabled: false,
        aiEnabled: false
      }
    }
  }
}

/**
 * 配置分析器选项
 * @param {Object} config - 配置选项
 */
export async function configureAnalyzer(config = {}) {
  const constantsModule = await import('./constants.js')

  // 更新AI配置
  if (config.ai) {
    Object.assign(constantsModule.AI_CONFIG, config.ai)
  }

  // 更新性能配置
  if (config.performance) {
    Object.assign(constantsModule.PERFORMANCE_CONFIG, config.performance)
  }

  console.log('物流状态分析器配置已更新')
}

/**
 * 重置分析器到默认状态
 */
export async function resetAnalyzer() {
  const utilsModule = await import('./utils.js')

  utilsModule.clearAllCache()
  console.log('物流状态分析器已重置')
}

// 默认导出主要分析函数
export default async function(orderTrack, options = {}) {
  const analyzerModule = await import('./analyzer.js')
  return analyzerModule.analyzeLogisticsStatus(orderTrack, options)
}
