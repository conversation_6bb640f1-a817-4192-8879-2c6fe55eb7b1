/**
 * 物流状态分析器 - 常量定义
 * 定义物流状态枚举、文本映射等常量
 */

// 物流状态枚举
export const LOGISTICS_STATUS = {
  PENDING_PICKUP: 'pending_pickup',     // 待揽件
  PICKED_UP: 'picked_up',               // 已揽件
  IN_TRANSIT: 'in_transit',             // 运输中
  OUT_FOR_DELIVERY: 'out_for_delivery', // 派送中
  DELIVERED: 'delivered',               // 已签收
  FAILED_DELIVERY: 'failed_delivery',   // 派送失败
  RETURNED: 'returned',                 // 已退回
  EXCEPTION: 'exception'                // 异常
}

// 状态显示文本映射
export const STATUS_TEXT_MAP = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: '待揽件',
  [LOGISTICS_STATUS.PICKED_UP]: '已揽收',
  [LOGISTICS_STATUS.IN_TRANSIT]: '运输中',
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: '派送中',
  [LOGISTICS_STATUS.DELIVERED]: '已签收',
  [LOGISTICS_STATUS.FAILED_DELIVERY]: '派送失败',
  [LOGISTICS_STATUS.RETURNED]: '已退回',
  [LOGISTICS_STATUS.EXCEPTION]: '异常'
}

// 状态紧急程度映射
export const STATUS_URGENCY_MAP = {
  [LOGISTICS_STATUS.EXCEPTION]: 5,
  [LOGISTICS_STATUS.FAILED_DELIVERY]: 4,
  [LOGISTICS_STATUS.RETURNED]: 4,
  [LOGISTICS_STATUS.PENDING_PICKUP]: 3,
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 3,
  [LOGISTICS_STATUS.IN_TRANSIT]: 2,
  [LOGISTICS_STATUS.PICKED_UP]: 2,
  [LOGISTICS_STATUS.DELIVERED]: 1
}

// 终态状态集合
export const FINAL_STATUSES = new Set([
  LOGISTICS_STATUS.DELIVERED,
  LOGISTICS_STATUS.RETURNED,
  LOGISTICS_STATUS.EXCEPTION
])

// 分析方法类型
export const ANALYSIS_METHODS = {
  DEFAULT: 'default',
  LATEST_RECORD_HIGH_CONFIDENCE: 'latest_record_high_confidence',
  FULL_TRACK_ANALYSIS: 'full_track_analysis',
  INFERRED_FROM_HISTORY: 'inferred_from_history',
  AI_ENHANCED: 'ai_enhanced',
  FALLBACK: 'fallback'
}

// AI模型配置
export const AI_CONFIG = {
  TENSORFLOW_MODEL_URL: '/models/logistics-classifier',
  FALLBACK_ENABLED: true,
  CONFIDENCE_THRESHOLD: 0.7,
  TIMEOUT_MS: 3000,
  MAX_RETRIES: 2
}

// 性能配置
export const PERFORMANCE_CONFIG = {
  MAX_TRACK_ANALYSIS: 5,        // 最多分析的轨迹条数
  CACHE_ENABLED: true,          // 启用缓存
  CACHE_TTL: 300000,           // 缓存5分钟
  BATCH_SIZE: 10               // 批处理大小
}

// 置信度阈值配置
export const CONFIDENCE_THRESHOLDS = {
  HIGH: 0.8,
  MEDIUM: 0.5,
  LOW: 0.3,
  MIN: 0.1
}

// 权重配置
export const WEIGHT_CONFIG = {
  KEYWORD_MATCH: {
    HIGH: 30,
    MEDIUM: 15,
    LOW: 8
  },
  AI_ANALYSIS: 25,
  SEMANTIC_ANALYSIS: 15,
  TIME_FACTOR: 10,
  CONTEXT_COHERENCE: 5
}
