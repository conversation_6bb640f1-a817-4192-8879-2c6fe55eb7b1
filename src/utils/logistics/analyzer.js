/**
 * 物流状态分析器 - 核心分析模块
 * 整合AI、语义分析、关键词匹配等多种分析方法
 * 提供高性能、高准确率的物流状态分析
 */

import { 
  LOGISTICS_STATUS, 
  STATUS_TEXT_MAP, 
  CONFIDENCE_THRESHOLDS,
  ANALYSIS_METHODS,
  AI_CONFIG
} from './constants.js'
import { STATUS_KEYWORDS } from './keywords.js'
import { analyzeWithAI } from './aiAnalyzer.js'
import { enhancedSemanticAnalysis, predictStatusFromSemantic } from './semanticAnalyzer.js'
import { 
  validateStatusTransitions, 
  analyzeStatusConsistency,
  inferStatusFromHistory,
  isFinalStatus,
  selectMostLikelyNextStatus
} from './statusTransition.js'
import { 
  calculateKeywordScore,
  isAmbiguousContext,
  analyzeTimeFactors,
  formatAnalysisResult,
  withPerformanceMonitoring
} from './utils.js'

/**
 * 分析物流状态 - 主入口函数
 * @param {Array} orderTrack - 物流轨迹数组，按时间倒序排列
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
export async function analyzeLogisticsStatus(orderTrack, options = {}) {
  const {
    enableAI = true,
    enableCache = true,
    maxAnalysisDepth = 5,
    confidenceThreshold = CONFIDENCE_THRESHOLDS.MEDIUM
  } = options

  if (!orderTrack || orderTrack.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      latestTrack: null,
      confidence: 0,
      analysisMethod: ANALYSIS_METHODS.DEFAULT
    }
  }

  // 获取最新的轨迹记录
  const latestTrack = orderTrack[0]
  const context = latestTrack.context || latestTrack.content || ''

  try {
    // 优化策略：优先分析最新记录
    const latestAnalysis = await analyzeTrackContext(context, latestTrack, { enableAI })

    // 如果最新记录分析置信度足够高且不是模糊状态，直接返回结果
    if (latestAnalysis.confidence >= CONFIDENCE_THRESHOLDS.HIGH && !isAmbiguousContext(context)) {
      return formatAnalysisResult({
        ...latestAnalysis,
        latestTrack,
        analysisMethod: ANALYSIS_METHODS.LATEST_RECORD_HIGH_CONFIDENCE
      })
    }

    // 如果最新记录置信度不够或者是模糊状态，进行全轨迹分析
    const fullTrackAnalysis = await analyzeFullTrackHistory(
      orderTrack.slice(0, maxAnalysisDepth), 
      { enableAI }
    )

    // 比较两种分析结果，选择更可靠的
    const finalResult = selectBestAnalysis(latestAnalysis, fullTrackAnalysis)

    return formatAnalysisResult({
      ...finalResult,
      latestTrack,
      latestAnalysis,
      fullTrackAnalysis
    })
  } catch (error) {
    console.warn('物流状态分析失败:', error)
    return getFallbackResult(latestTrack, context)
  }
}

/**
 * 分析单条轨迹上下文
 * @param {string} context - 轨迹描述文本
 * @param {Object} trackRecord - 完整的轨迹记录
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
const analyzeTrackContext = withPerformanceMonitoring(async function(context, trackRecord = {}, options = {}) {
  const { enableAI = true } = options
  
  let maxScore = 0
  let detectedStatus = LOGISTICS_STATUS.PENDING_PICKUP
  let statusScores = {}

  // 1. 关键词匹配分析
  for (const [status, keywords] of Object.entries(STATUS_KEYWORDS)) {
    const score = calculateKeywordScore(context, keywords)
    statusScores[status] = score

    if (score > maxScore) {
      maxScore = score
      detectedStatus = status
    }
  }

  // 保存原始关键词匹配结果
  const keywordMatchResult = {
    status: detectedStatus,
    score: maxScore,
    confidence: Math.min(maxScore / 50, 1)
  }

  // 2. 语义增强分析
  const semanticResult = await enhancedSemanticAnalysis(context)

  // 3. 时间因素分析
  const timeAnalysis = analyzeTimeFactors(trackRecord)

  // 4. AI分析（如果启用）
  let aiResult = null
  if (enableAI && AI_CONFIG.FALLBACK_ENABLED) {
    try {
      aiResult = await analyzeWithAI(context, trackRecord)
    } catch (error) {
      console.warn('AI分析失败，继续使用传统方法:', error.message)
    }
  }

  // 融合所有分析结果
  const fusedResult = fuseAnalysisResults({
    keywordMatch: keywordMatchResult,
    semantic: semanticResult,
    timeAnalysis,
    ai: aiResult
  }, context)

  return {
    ...fusedResult,
    keywordScores: statusScores,
    semanticAnalysis: semanticResult,
    timeAnalysis,
    aiAnalysis: aiResult,
    analysisMethod: ANALYSIS_METHODS.LATEST_RECORD_HIGH_CONFIDENCE
  }
}, 'analyzeTrackContext')

/**
 * 分析完整物流轨迹历史
 * @param {Array} orderTrack - 物流轨迹数组
 * @param {Object} options - 分析选项
 * @returns {Promise<Object>} 分析结果
 */
const analyzeFullTrackHistory = withPerformanceMonitoring(async function(orderTrack, options = {}) {
  if (!orderTrack || orderTrack.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      confidence: 0,
      analysisMethod: ANALYSIS_METHODS.DEFAULT
    }
  }

  // 分析状态转换序列
  const statusSequence = []
  const trackAnalyses = []
  const confidenceSequence = []

  // 分析每条轨迹记录
  for (let i = 0; i < orderTrack.length; i++) {
    const track = orderTrack[i]
    const context = track.context || track.content || ''
    const analysis = await analyzeTrackContext(context, track, options)

    trackAnalyses.push({
      ...analysis,
      trackIndex: i,
      timestamp: track.msgTime || track.time,
      isLatest: i === 0
    })

    statusSequence.push(analysis.status)
    confidenceSequence.push(analysis.confidence)
  }

  // 状态一致性检查
  const consistencyAnalysis = analyzeStatusConsistency(statusSequence)

  // 状态转换逻辑验证
  const transitionAnalysis = validateStatusTransitions(statusSequence)

  // 综合分析得出最终状态
  const finalStatus = determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis)

  // 如果最新记录很模糊，尝试从历史轨迹推断当前状态
  if (trackAnalyses.length > 1 && trackAnalyses[0].confidence < CONFIDENCE_THRESHOLDS.MEDIUM) {
    const inferredStatus = inferStatusFromHistory(trackAnalyses)
    if (inferredStatus && inferredStatus.confidence > finalStatus.confidence) {
      return {
        ...inferredStatus,
        statusSequence,
        trackAnalyses,
        consistencyAnalysis,
        transitionAnalysis,
        analysisMethod: ANALYSIS_METHODS.INFERRED_FROM_HISTORY
      }
    }
  }

  return {
    ...finalStatus,
    statusSequence,
    trackAnalyses,
    consistencyAnalysis,
    transitionAnalysis,
    analysisMethod: ANALYSIS_METHODS.FULL_TRACK_ANALYSIS
  }
}, 'analyzeFullTrackHistory')

/**
 * 融合多种分析结果
 * @param {Object} results - 各种分析结果
 * @param {string} context - 原始文本
 * @returns {Object} 融合后的结果
 */
function fuseAnalysisResults(results, context) {
  const { keywordMatch, semantic, timeAnalysis, ai } = results
  
  let finalStatus = keywordMatch.status
  let finalConfidence = keywordMatch.confidence
  let finalScore = keywordMatch.score

  // 语义分析加成
  if (semantic && semantic.semanticScore !== 0) {
    finalScore += semantic.semanticScore
    
    // 语义预测状态
    const semanticPrediction = predictStatusFromSemantic(semantic, context)
    if (semanticPrediction && semanticPrediction.confidence > 0.7) {
      if (semanticPrediction.status === finalStatus) {
        // 状态一致，提升置信度
        finalScore += 8
      } else if (keywordMatch.score < 25) {
        // 关键词匹配得分不高时，考虑语义建议
        if (semanticPrediction.confidence > finalConfidence) {
          finalStatus = semanticPrediction.status
          finalScore = semanticPrediction.confidence * 50
        }
      }
    }
  }

  // 时间因素加成
  if (timeAnalysis && timeAnalysis.timeBonus) {
    finalScore += timeAnalysis.timeBonus
  }

  // AI分析融合
  if (ai && ai.aiEnabled && ai.confidence > 0.6) {
    if (ai.status === finalStatus) {
      // AI与其他方法一致，提升置信度
      finalScore += 10
    } else if (ai.confidence > finalConfidence + 0.2) {
      // AI置信度明显更高，采用AI结果
      finalStatus = ai.status
      finalScore = ai.confidence * 50
    }
  }

  // 计算最终置信度
  finalConfidence = Math.min(finalScore / 50, 1)
  finalConfidence = Math.max(finalConfidence, 0.1)

  return {
    status: finalStatus,
    statusText: STATUS_TEXT_MAP[finalStatus],
    confidence: finalConfidence,
    rawScore: finalScore,
    fusionMethod: 'weighted_combination'
  }
}

/**
 * 选择最佳分析结果
 * @param {Object} latestAnalysis - 最新记录分析结果
 * @param {Object} fullTrackAnalysis - 全轨迹分析结果
 * @returns {Object} 最终分析结果
 */
function selectBestAnalysis(latestAnalysis, fullTrackAnalysis) {
  // 如果最新记录置信度很高且是终态，优先选择
  if (latestAnalysis.confidence >= CONFIDENCE_THRESHOLDS.HIGH && isFinalStatus(latestAnalysis.status)) {
    return {
      ...latestAnalysis,
      analysisMethod: 'latest_high_confidence_final'
    }
  }

  // 如果全轨迹分析置信度更高，选择全轨迹结果
  if (fullTrackAnalysis.confidence > latestAnalysis.confidence + 0.2) {
    return {
      ...fullTrackAnalysis,
      analysisMethod: 'full_track_higher_confidence'
    }
  }

  // 如果状态一致且都有合理置信度，选择置信度更高的
  if (latestAnalysis.status === fullTrackAnalysis.status) {
    const bestAnalysis = latestAnalysis.confidence >= fullTrackAnalysis.confidence
      ? latestAnalysis
      : fullTrackAnalysis

    return {
      ...bestAnalysis,
      confidence: Math.max(latestAnalysis.confidence, fullTrackAnalysis.confidence),
      analysisMethod: 'consistent_status_best_confidence'
    }
  }

  // 默认选择最新记录分析，但降低置信度
  return {
    ...latestAnalysis,
    confidence: Math.max(latestAnalysis.confidence * 0.8, 0.1),
    analysisMethod: 'latest_with_reduced_confidence'
  }
}

/**
 * 确定最终状态
 * @param {Array} trackAnalyses - 轨迹分析结果数组
 * @param {Object} consistencyAnalysis - 一致性分析结果
 * @param {Object} transitionAnalysis - 转换分析结果
 * @returns {Object} 最终状态结果
 */
function determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis) {
  if (trackAnalyses.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      confidence: 0
    }
  }

  // 获取最新记录的分析结果
  const latestAnalysis = trackAnalyses[0]

  // 如果最新记录是终态且置信度高，直接返回
  if (isFinalStatus(latestAnalysis.status) && latestAnalysis.confidence >= 0.7) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: Math.min(latestAnalysis.confidence + 0.1, 1)
    }
  }

  // 如果状态转换逻辑有效且一致性好，使用主导状态
  if (transitionAnalysis.isValid && consistencyAnalysis.isConsistent) {
    const finalStatus = consistencyAnalysis.dominantStatus
    const baseConfidence = Math.max(
      latestAnalysis.confidence,
      consistencyAnalysis.confidence * 0.8
    )

    return {
      status: finalStatus,
      statusText: STATUS_TEXT_MAP[finalStatus],
      confidence: Math.min(baseConfidence + 0.15, 1)
    }
  }

  // 如果转换逻辑无效，但最新记录置信度可接受，使用最新记录
  if (latestAnalysis.confidence >= CONFIDENCE_THRESHOLDS.MEDIUM) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: latestAnalysis.confidence * 0.9 // 略微降低置信度
    }
  }

  // 最后的兜底策略：使用主导状态但降低置信度
  const fallbackStatus = consistencyAnalysis.dominantStatus || LOGISTICS_STATUS.PENDING_PICKUP
  return {
    status: fallbackStatus,
    statusText: STATUS_TEXT_MAP[fallbackStatus],
    confidence: Math.max(consistencyAnalysis.confidence * 0.6, 0.1)
  }
}

/**
 * 获取兜底分析结果
 * @param {Object} latestTrack - 最新轨迹记录
 * @param {string} context - 轨迹描述
 * @returns {Object} 兜底结果
 */
function getFallbackResult(latestTrack, context) {
  return formatAnalysisResult({
    status: LOGISTICS_STATUS.PENDING_PICKUP,
    statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
    confidence: 0.1,
    latestTrack,
    analysisMethod: ANALYSIS_METHODS.FALLBACK,
    error: '分析过程中发生错误，使用默认状态'
  })
}
