/**
 * 物流状态语义分析器 - 主入口
 * 根据物流轨迹信息智能分析当前物流状态
 * 集成AI和多种语义分析库进行增强分析
 * 
 * 🚀 新版本特性：
 * - 大幅扩充中文关键词库，移除英文关键词
 * - 集成TensorFlow.js、Natural、Jieba等AI分析库
 * - 完善的错误处理和兜底机制
 * - 高性能缓存和批处理支持
 * - 模块化架构，易于维护和扩展
 * 
 * @deprecated 此文件将被重构为模块化结构，请使用 src/utils/logistics/ 目录下的新模块
 */

// 重新导出新模块的功能，保持向后兼容
export * from './logistics/index.js'

// 为了完全向后兼容，保留原有的主要导出
export { 
  analyzeLogisticsStatus,
  getDetailedLogisticsAnalysis,
  LOGISTICS_STATUS,
  STATUS_TEXT_MAP,
  isFinalStatus,
  getStatusUrgency
} from './logistics/index.js'
